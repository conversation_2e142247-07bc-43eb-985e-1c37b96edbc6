C51 COMPILER V9.60.0.0   DEMO_TIMER                                                        07/18/2025 16:33:50 PAGE 1   


C51 COMPILER V9.60.0.0, COMPILATION OF MODULE DEMO_TIMER
OBJECT MODULE PLACED IN .\Objects\demo_timer.obj
COMPILER INVOKED BY: D:\KeilC51\C51\BIN\C51.EXE ..\code\demo_timer.c LARGE OMF2 OPTIMIZE(8,SPEED) BROWSE INCDIR(..\code;
                    -..\Libary\Device\CMS8S6990\Include;..\Libary\StdDriver\inc) DEFINE(USE_FORMULA) DEBUG PRINT(.\Listings\demo_timer.lst) T
                    -ABS(2) OBJECT(.\Objects\demo_timer.obj)

line level    source

   1          #include "demo_timer.h"
   2          
   3          void TMR0_Config(void)
   4          {
   5   1        /*
   6   1        (1)设置Timer的运行模式
   7   1        */
   8   1        TMR_ConfigRunMode(TMR0, TMR_MODE_TIMING,TMR_TIM_AUTO_8BIT); 
   9   1        /*
  10   1        (2)设置Timer 运行时钟
  11   1        */
  12   1        TMR_ConfigTimerClk(TMR0, TMR_CLK_DIV_12);           /*Fsys = 24Mhz，Ftimer = 2Mhz,Ttmr=0.5us*/
  13   1        /*
  14   1        (3)设置Timer周期
  15   1        */  
  16   1        TMR_ConfigTimerPeriod(TMR0, 256-100, 256-100);        // 100*0.5us = 50us,递增计数
  17   1          
  18   1        /*
  19   1        (4)开启中断
  20   1        */
  21   1        TMR_EnableOverflowInt(TMR0);
  22   1      
  23   1        /*
  24   1        (5)设置Timer中断优先级
  25   1        */  
  26   1        IRQ_SET_PRIORITY(IRQ_TMR0,IRQ_PRIORITY_HIGH);
  27   1        IRQ_ALL_ENABLE(); 
  28   1      
  29   1        /*
  30   1        (6)开启Timer
  31   1        */
  32   1        TMR_Start(TMR0);
  33   1      }
  34          
  35          void TMR1_Config(void)
  36          {
  37   1        /*
  38   1        (1)设置Timer的运行模式
  39   1        */
  40   1        TMR_ConfigRunMode(TMR1, TMR_MODE_TIMING,TMR_TIM_16BIT); 
  41   1        /*
  42   1        (2)设置Timer 运行时钟
  43   1        */
  44   1        TMR_ConfigTimerClk(TMR1, TMR_CLK_DIV_12);           /*Fsys = 24Mhz，Ftimer = 2Mhz,Ttmr=0.5us*/
  45   1        /*
  46   1        (3)设置Timer周期
  47   1        */  
  48   1        TMR_ConfigTimerPeriod(TMR1, (65536-20000)>>8, 65536-20000);         // 20000*0.5us = 10ms,递增计数
  49   1          
  50   1        /*
  51   1        (4)开启中断
  52   1        */
  53   1        TMR_EnableOverflowInt(TMR1);
C51 COMPILER V9.60.0.0   DEMO_TIMER                                                        07/18/2025 16:33:50 PAGE 2   

  54   1      
  55   1        /*
  56   1        (5)设置Timer中断优先级
  57   1        */  
  58   1        IRQ_SET_PRIORITY(IRQ_TMR1,IRQ_PRIORITY_LOW);
  59   1        IRQ_ALL_ENABLE(); 
  60   1      
  61   1        /*
  62   1        (6)开启Timer
  63   1        */
  64   1        TMR_Start(TMR1);
  65   1      }
  66          
  67          void TMR4_Config(void)
  68          {
  69   1        /*
  70   1        (1)设置Timer的运行模式
  71   1        */
  72   1        TMR_ConfigRunMode(TMR4, TMR_MODE_TIMING,TMR_TIM_16BIT); 
  73   1        /*
  74   1        (2)设置Timer 运行时钟
  75   1        */
  76   1        TMR_ConfigTimerClk(TMR4, TMR_CLK_DIV_12);           /*Fsys = 24Mhz，Ftimer = 2Mhz,Ttmr=0.5us*/
  77   1        /*
  78   1        (3)设置Timer周期
  79   1        */  
  80   1        TMR_ConfigTimerPeriod(TMR4, (65536-2000)>>8, 65536-2000);         // 2000*0.5us = 1000us,递增计数
  81   1          
  82   1        /*
  83   1        (5)开启中断
  84   1        */
  85   1        TMR_EnableOverflowInt(TMR4);
  86   1      
  87   1        /*
  88   1        (6)设置Timer中断优先级
  89   1        */  
  90   1        IRQ_SET_PRIORITY(IRQ_TMR4,IRQ_PRIORITY_LOW);
  91   1        IRQ_ALL_ENABLE(); 
  92   1      
  93   1        /*
  94   1        (8)开启Timer
  95   1        */
  96   1        TMR_Start(TMR4);
  97   1      }
  98          
  99          
 100          
 101          
 102          
 103          
 104          
 105          
 106          
 107          
 108          
 109          
 110          
 111          
 112          
 113          
 114          
 115          
C51 COMPILER V9.60.0.0   DEMO_TIMER                                                        07/18/2025 16:33:50 PAGE 3   

 116          
 117          
 118          
 119          
 120          
 121          
 122          
 123          


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    124    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
   EDATA SIZE       =   ----    ----
   HDATA SIZE       =   ----    ----
   XDATA CONST SIZE =   ----    ----
   FAR CONST SIZE   =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
