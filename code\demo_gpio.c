/*******************************************************************************
* Copyright (C) 2019 China Micro Semiconductor Limited Company. All Rights Reserved.
*
* This software is owned and published by:
* CMS LLC, No 2609-10, Taurus Plaza, TaoyuanRoad, NanshanDistrict, Shenzhen, China.
*
* BY DOWNLOADING, INSTALLING OR USING THIS SOFTWARE, YOU AGREE TO BE BOUND
* BY ALL THE TERMS AND CONDITIONS OF THIS AGREEMENT.
*
* This software contains source code for use with CMS
* components. This software is licensed by CMS to be adapted only
* for use in systems utilizing CMS components. CMS shall not be
* responsible for misuse or illegal use of this software for devices not
* supported herein. CMS is providing this software "AS IS" and will
* not be responsible for issues arising from incorrect user implementation
* of the software.
*
* This software may be replicated in part or whole for the licensed use,
* with the restriction that this Disclaimer and Copyright notice must be
* included with each copy of this software, whether used in part or whole,
* at all times.
*/

/****************************************************************************/
/** \file demo_gpio.c
**
**  
**
**	History:
**	
*****************************************************************************/
/****************************************************************************/
/*	include files
*****************************************************************************/
#include "demo_gpio.h"

/****************************************************************************/
/*	Local pre-processor symbols('#define')
****************************************************************************/

/****************************************************************************/
/*	Global variable definitions(declared in header file with 'extern')
****************************************************************************/

/****************************************************************************/
/*	Local type definitions('typedef')
****************************************************************************/

/****************************************************************************/
/*	Local variable  definitions('static')
****************************************************************************/

/****************************************************************************/
/*	Local function prototypes('static')
****************************************************************************/

/****************************************************************************/
/*	Function implementation - global ('extern') and local('static')
****************************************************************************/
extern bit uart;
/******************************************************************************
 ** \brief	 GPIO_Config
 ** \param [in] none
 **          GPIO中断功能
 ** \return  none
 ** \note  
 ******************************************************************************/
void GPIO_Config(void)
{
	P0TRIS=0xff;P0=0x0;P1TRIS=0xff;P1=0x0;P2TRIS=0xff;P2=0x0;P3TRIS=0xff;P3=0x0;
  
	//A1-P17 A2-P16 B1-P15 B2-P14
	GPIO_SET_MUX_MODE(P17CFG,GPIO_MUX_GPIO);
	GPIO_SET_MUX_MODE(P16CFG,GPIO_MUX_GPIO);
	GPIO_SET_MUX_MODE(P15CFG,GPIO_MUX_GPIO);
	GPIO_SET_MUX_MODE(P14CFG,GPIO_MUX_GPIO);
	GPIO_ENABLE_OUTPUT(P1TRIS,GPIO_PIN_7);
	GPIO_ENABLE_OUTPUT(P1TRIS,GPIO_PIN_6);
	GPIO_ENABLE_OUTPUT(P1TRIS,GPIO_PIN_5);
	GPIO_ENABLE_OUTPUT(P1TRIS,GPIO_PIN_4);
	
	//P22-LEDG P23-LEDR
	GPIO_SET_MUX_MODE(P22CFG, GPIO_MUX_GPIO);		
	GPIO_SET_MUX_MODE(P23CFG, GPIO_MUX_GPIO);		
	GPIO_ENABLE_OUTPUT(P2TRIS, GPIO_PIN_2);			
	GPIO_ENABLE_OUTPUT(P2TRIS, GPIO_PIN_3);		
	P22=0;P23=0;
	
	//K1-P05 K2-P04 K3-P13
	GPIO_SET_MUX_MODE(P05CFG, GPIO_MUX_GPIO);		//设置为GPIO模式
	GPIO_ENABLE_INPUT(P0TRIS, GPIO_PIN_5);			//设置为输入模式
	GPIO_ENABLE_UP(P0UP, GPIO_PIN_5);				//开启上拉
  
 	GPIO_SET_MUX_MODE(P04CFG, GPIO_MUX_GPIO);		//设置为GPIO模式
	GPIO_ENABLE_INPUT(P0TRIS, GPIO_PIN_4);			//设置为输入模式
	GPIO_ENABLE_UP(P0UP, GPIO_PIN_4); 
  
	GPIO_SET_MUX_MODE(P13CFG, GPIO_MUX_GPIO);		//设置为GPIO模式
	GPIO_ENABLE_INPUT(P1TRIS, GPIO_PIN_3);			//设置为输入模式
	GPIO_ENABLE_UP(P1UP, GPIO_PIN_3);
  
	//K2设置下降沿中断
	GPIO_SET_INT_MODE(P04EICFG, GPIO_INT_FALLING);	//设置为下降沿中断模式
	GPIO_EnableInt(GPIO0, GPIO_PIN_4_MSK);			//开启P36中断
	IRQ_SET_PRIORITY(IRQ_P0, IRQ_PRIORITY_LOW);

	
	//RF_DATA-P25  RF_EN-P24
	GPIO_SET_MUX_MODE(P25CFG, GPIO_MUX_GPIO);	
	GPIO_ENABLE_INPUT(P2TRIS, GPIO_PIN_5);
	GPIO_SET_MUX_MODE(P24CFG, GPIO_MUX_GPIO);
	GPIO_ENABLE_OUTPUT(P2TRIS,GPIO_PIN_4);
	P24=1;
	
	//CHG_CHARGE-P36  CHG_FULL-P32
	GPIO_SET_MUX_MODE(P36CFG,GPIO_MUX_GPIO);
	GPIO_SET_MUX_MODE(P32CFG,GPIO_MUX_GPIO);
	GPIO_ENABLE_INPUT(P3TRIS,GPIO_PIN_6);
	GPIO_ENABLE_INPUT(P3TRIS,GPIO_PIN_2);
	GPIO_ENABLE_UP(P3UP,GPIO_PIN_6);
	GPIO_ENABLE_UP(P3UP,GPIO_PIN_2);
	
	//P32设置下降沿中断
	GPIO_SET_INT_MODE(P32EICFG, GPIO_INT_FALLING);	//设置为下降沿中断模式
	GPIO_EnableInt(GPIO3, GPIO_PIN_2_MSK);			//开启中断
	IRQ_SET_PRIORITY(IRQ_P3, IRQ_PRIORITY_LOW);
	//P36设置下降沿中断
	GPIO_SET_INT_MODE(P36EICFG, GPIO_INT_FALLING);	//设置为下降沿中断模式
	GPIO_EnableInt(GPIO3, GPIO_PIN_6_MSK);			//开启中断
	IRQ_SET_PRIORITY(IRQ_P3, IRQ_PRIORITY_LOW);
	
	
	IRQ_ALL_ENABLE();

}


//编码格式UTF-8





















